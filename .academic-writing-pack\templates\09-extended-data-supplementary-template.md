# 扩展数据与补充材料模板 (Extended Data and Supplementary Materials Template)

[[LLM: Based on analysis of high-impact publications like Nature Medicine, focus on comprehensive supplementary data organization, detailed technical information, and additional validation results. Emphasize data transparency, reproducibility, and comprehensive documentation.]]

## 模板说明
- **用途**: 学术论文扩展数据和补充材料部分
- **适用范围**: 高影响因子期刊、技术创新研究、临床验证研究
- **核心原则**: 数据完整性、技术透明性、结果可重现性、信息补充性
- **结构**: 系统表征→技术细节→临床数据→统计分析→方法验证
- **写作风格**: 详实准确、逻辑清晰、数据丰富、技术规范

[[LLM: Organize by data types and technical categories. Include detailed experimental protocols, comprehensive datasets, and thorough statistical analyses.]]

## 1. 系统表征与性能测试

### 1.1 环境稳定性测试
[[LLM: Present comprehensive system characterization under various environmental conditions.]]

**模板结构**:
```
**补充图{{figure_number}}. {{sensor_type}}和{{component_type}}的热稳定性测试。**
(a) 热测试设置的照片。(b) 在两种不同温度下捕获的{{measurement_type}}（n = {{data_points}}数据点）和 (c) {{measurement_type_2}}（n = {{data_points}}数据点）图。(d) {{parameter_list}}数据随温度从{{temp_range}}的变化。(e) 在{{temp_1}}°C和 (f) {{temp_2}}°C时的{{detailed_measurements}}数据的放大视图。箱线图显示第25和第75百分位数之间的范围。须状线显示第5和第95百分位数之间的范围。中线表示每个数据集的中位数。
```

**写作要点**:
- 提供详细的测试设置和条件
- 包含具体的数据点数量
- 使用标准的统计图表表示方法
- 强调测试的全面性和准确性

**示例**:
```
**补充图1. 麦克风和IMU的热稳定性测试。**
(a) 热测试设置的照片。(b) 在两种不同温度下捕获的加速度（n = 51数据点）和 (c) 声音强度（n = 51数据点）图。(d) 加速度、麦克风和温度数据随温度从32°C到40°C的变化。(e) 在32°C和 (f) 40°C时的加速度、麦克风和声音强度数据的放大视图。
```

### 1.2 功耗与充电特性
[[LLM: Detail power consumption characteristics and wireless charging performance.]]

**模板结构**:
```
**补充图{{figure_number}}. 功耗和无线充电数据。**
(a) 设备{{operation_modes}}期间的电流消耗和 (b) {{time_interval}}平均电流。(c) 从完全充电的电池开始的操作时间函数上的电池剩余电压。(d) 无线充电期间的电池电压。{{additional_analysis}}显示{{performance_metrics}}，确保了{{safety_considerations}}。
```

**写作要点**:
- 详细记录不同工作模式的功耗
- 提供电池性能的完整数据
- 包含无线充电的安全性验证
- 强调长期使用的可靠性

**示例**:
```
**补充图2. 功耗和无线充电数据。**
(a) 设备待机和运行期间的电流消耗和 (b) 1秒平均电流。(c) 从完全充电的电池开始的操作时间函数上的电池剩余电压。(d) 无线充电期间的电池电压。温度监测显示充电过程中温差保持在0.5°C以内，确保了使用安全性。
```

## 2. 算法验证与对比分析

### 2.1 声音分离算法验证
[[LLM: Provide detailed validation of sound separation algorithms with comprehensive performance metrics.]]

**模板结构**:
```
**扩展数据图{{figure_number}} | 使用{{system_name}}系统进行声音分离的表征。**
(a, b) 使用{{test_equipment}}的实验设置，(a) {{system_name}}系统和(b) {{reference_system}}。
(c) 在{{frequency_range}}频率范围内的{{noise_level}} dB{{noise_type}}环境中记录的{{signal_types}}强度。
(d, e) 在不同环境条件下测量的{{target_signals}}的信噪比（SNR），包括(d) {{condition_1}}（n={{data_points}}个数据点）和(e) {{condition_2}}（n={{data_points}}个数据点）。数据以SNR的均值±标准偏差表示。
```

**写作要点**:
- 提供详细的实验设置对比
- 包含具体的测试条件和参数
- 使用标准的性能评价指标
- 强调算法的优越性能

**示例**:
```
**扩展数据图3 | 使用BAMS系统进行声音分离的表征。**
(a, b) 使用肺声音训练器和声级计的实验设置，(a) BAMS系统和(b) 带有主动噪声消除的商业数字听诊器。
(c) 在20到400 Hz频率范围内的90 dB白噪声环境中记录的呼吸声音和心脏声音强度。
(d, e) 在不同环境条件下测量的呼吸声音和心脏声音的信噪比（SNR），包括(d) 白噪声水平（n=50个数据点）和(e) 噪声水平为75 dB的声音类型（n=50个数据点）。
```

### 2.2 临床对比验证
[[LLM: Present comprehensive clinical validation data with statistical analyses.]]

**模板结构**:
```
**扩展数据图{{figure_number}} | 在{{clinical_setting}}中收集的{{parameter_name}}数据与标准测量的比较。**
Bland-Altman图比较使用{{new_system}}确定的{{target_parameter}}与{{reference_method}}测量（{{patient_number}}名{{patient_type}}，{{total_data_points}}个数据点）。平均差异为{{mean_difference}}，标准偏差为{{standard_deviation}}，95%一致性界限为{{confidence_limits}}。
```

**写作要点**:
- 使用标准的临床对比分析方法
- 提供详细的统计分析结果
- 包含具体的患者数量和数据点
- 强调临床验证的可靠性

**示例**:
```
**扩展数据图8 | 在NICU中收集的呼吸频率数据与标准测量的比较。**
Bland-Altman图比较使用BAMS系统确定的呼吸频率与鼻温流量测量（5名新生儿，42,738个数据点）。平均差异为0.06 bpm，标准偏差为1.92 bpm，95%一致性界限为-3.7到3.8 bpm。
```

## 3. 详细技术实现

### 3.1 时间同步网络验证
[[LLM: Detail the technical implementation and validation of time synchronization networks.]]

**模板结构**:
```
**补充图{{figure_number}}. {{system_name}}系统无线网络的时间同步。**
(a) 时间同步方案的框图。(b) 使用{{device_number}}个{{device_name}}设备和{{test_equipment}}的测试设置照片。(c) 由{{device_number}}个{{device_name}}设备记录的{{signal_type}}数据。(d) 交叉相关结果和{{device_name}}设备之间的时间差异。(e) {{device_name}}设备之间的平均时间差异（n = {{data_points}}数据点）。数据以设备之间时间差异的均值±标准偏差表示。
```

**写作要点**:
- 提供详细的技术实现框图
- 包含实际测试设置的照片证据
- 使用交叉相关等标准分析方法
- 量化时间同步的精度

**示例**:
```
**补充图16. BAMS系统无线网络的时间同步。**
(a) 时间同步方案的框图。(b) 使用13个BAMS设备和振动发生器的测试设置照片。(c) 由13个BAMS设备记录的声音数据。(d) 交叉相关结果和BAMS设备之间的时间差异。(e) BAMS设备之间的平均时间差异（n = 1350数据点）。
```

### 3.2 信号处理算法细节
[[LLM: Provide detailed signal processing algorithms and parameter specifications.]]

**模板结构**:
```
**补充图{{figure_number}}. {{algorithm_name}}的数据处理步骤流程图。**
流程图显示从{{input_data}}到{{output_results}}的完整处理流程，包括：
1. {{step_1}}：{{parameter_settings_1}}
2. {{step_2}}：{{parameter_settings_2}}
3. {{step_3}}：{{parameter_settings_3}}
4. {{step_4}}：{{parameter_settings_4}}
每个步骤的具体参数设置和阈值标准详见方法部分。
```

**写作要点**:
- 提供完整的算法流程图
- 详细说明每个处理步骤
- 包含具体的参数设置
- 确保算法的可重现性

**示例**:
```
**补充图15. 检测肠鸣音的数据处理步骤流程图。**
流程图显示从原始声音数据到肠鸣音峰值检测的完整处理流程，包括：
1. 带通滤波：150-400 Hz频率范围
2. 短时傅里叶变换：窗口大小0.03秒，重叠0.027秒
3. 强度计算：>150 Hz频率范围积分
4. 峰值检测：宽度<100 ms，强度>20 dB，加速度<0.1g
```

## 4. 临床数据详细分析

### 4.1 患者人口统计学特征
[[LLM: Present comprehensive patient demographics and clinical characteristics.]]

**模板结构**:
```
**扩展数据表{{table_number}} | 研究参与者详细信息**

| 研究编号 | {{demographic_1}} | {{demographic_2}} | {{clinical_parameter_1}} | {{clinical_parameter_2}} | {{additional_info}} |
|---------|------------------|------------------|------------------------|------------------------|-------------------|
| {{id_1}} | {{value_1_1}}    | {{value_1_2}}    | {{value_1_3}}          | {{value_1_4}}          | {{value_1_5}}     |
| {{id_2}} | {{value_2_1}}    | {{value_2_2}}    | {{value_2_3}}          | {{value_2_4}}          | {{value_2_5}}     |

包含详细的纳入和排除标准，以及每个参与者的具体临床特征。
```

**写作要点**:
- 提供完整的患者基本信息
- 包含相关的临床参数
- 确保数据的完整性和准确性
- 保护患者隐私的同时提供必要信息

### 4.2 长期监测数据
[[LLM: Present long-term monitoring data with comprehensive analysis.]]

**模板结构**:
```
**扩展数据图{{figure_number}} | 睡眠和剧烈活动期间的连续长期{{monitoring_type}}监测。**
{{signal_types}}的频谱图像和{{parameter_list}}的时间序列结果。监测时间跨度{{duration}}，数据采集频率{{sampling_rate}}，包含{{activity_types}}等不同活动状态下的连续记录。
```

**写作要点**:
- 展示长期连续监测的能力
- 包含不同活动状态的数据
- 提供详细的技术参数
- 体现系统的稳定性和可靠性

**示例**:
```
**扩展数据图5 | 睡眠和剧烈活动期间的连续长期心肺监测。**
心肺信号的频谱图像和活动、呼吸频率、呼吸声音强度、心率和心脏声音强度的时间序列结果。监测时间跨度12小时，数据采集频率1 kHz，包含睡眠、步行、运动等不同活动状态下的连续记录。
```

## 5. 方法学验证与重现性

### 5.1 实验重现性验证
[[LLM: Demonstrate experimental reproducibility with multiple trials and statistical validation.]]

**模板结构**:
```
**补充图{{figure_number}}. {{measurement_type}}的重现性验证。**
(a) 相同条件下{{trial_number}}次独立实验的结果比较。(b) 不同操作者进行的{{measurement_type}}结果一致性分析。(c) 设备间变异性评估（n = {{device_number}}个设备）。变异系数为{{cv_value}}%，组内相关系数为{{icc_value}}，表明良好的重现性。
```

**写作要点**:
- 提供多次独立实验的验证
- 评估操作者间和设备间的一致性
- 使用标准的统计指标评价重现性
- 确保研究结果的可靠性

### 5.2 统计分析方法
[[LLM: Detail statistical analysis methods and validation approaches.]]

**模板结构**:
```
使用{{software_name}}进行{{statistical_method}}，假设每组的数据点均呈正态分布。{{analysis_type}}分析涉及使用从{{participant_number}}名{{participant_type}}（编号{{id_range}}）收集的{{data_points}}个{{data_type}}数据点和{{reference_system}}数据。{{correlation_analysis}}利用了从{{study_population}}收集的累计{{total_data_points}}个{{measurement_type}}数据点。
```

**写作要点**:
- 明确统计分析软件和方法
- 详细说明数据分布假设
- 提供具体的样本量和数据点数
- 确保统计分析的科学性

**示例**:
```
使用MATLAB进行单因素多变量方差分析（MANOVA），假设每组的数据点均呈正态分布。新生儿心率分析涉及使用从五名新生儿（编号B001–B005）收集的136,013个身体声音数据点和FDA批准的ECG监测器数据。肺声音分析使用从20名健康参与者和35名慢性肺病患者（编号D001–D055）中收集的10,660个肺声音数据点进行。
```

## 质量检查清单

### 数据完整性
- [ ] 所有实验数据完整记录
- [ ] 统计分析方法明确
- [ ] 样本量和数据点数准确
- [ ] 缺失数据处理方法说明

### 技术透明性
- [ ] 详细的技术参数和设置
- [ ] 完整的算法流程和代码
- [ ] 实验设置的照片和图表
- [ ] 可重现的实验协议

### 验证充分性
- [ ] 多重验证方法的使用
- [ ] 与金标准的详细对比
- [ ] 重现性和一致性验证
- [ ] 统计显著性检验

### 补充价值
- [ ] 主文本未涵盖的重要信息
- [ ] 详细的技术实现细节
- [ ] 全面的性能评估数据
- [ ] 临床应用的补充证据
